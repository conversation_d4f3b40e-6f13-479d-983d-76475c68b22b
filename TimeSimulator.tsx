import React, { useState } from 'react';
import { useTime } from './TimeContext';

interface TimeSimulatorProps {
  // Pas de props pour le moment
}

const TimeSimulator: React.FC<TimeSimulatorProps> = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { simulatedTime, setSimulatedTime } = useTime();

  // Moments clés à tester
  const testTimes = [
    { label: '🌙 Minuit (00:00)', hour: 0, minute: 0 },
    { label: '🌄 Aube (05:30)', hour: 5, minute: 30 },
    { label: '🌅 Lever du soleil (06:30)', hour: 6, minute: 30 },
    { label: '☀️ Matin (09:00)', hour: 9, minute: 0 },
    { label: '🌞 Midi (12:00)', hour: 12, minute: 0 },
    { label: '🌤️ Après-midi (15:00)', hour: 15, minute: 0 },
    { label: '🌇 Coucher du soleil (18:30)', hour: 18, minute: 30 },
    { label: '🌆 Crépuscule (19:30)', hour: 19, minute: 30 },
    { label: '🌌 Nuit (22:00)', hour: 22, minute: 0 },
  ];

  const simulateTime = (hour: number, minute: number) => {
    const now = new Date();
    const simulated = new Date(now.getFullYear(), now.getMonth(), now.getDate(), hour, minute, 0);
    setSimulatedTime(simulated);

    console.log(`🕐 SIMULATION: Heure simulée = ${simulated.toLocaleTimeString()}`);

    // Arrêter la simulation après 10 secondes
    setTimeout(() => {
      setSimulatedTime(null);
      console.log('🕐 SIMULATION: Retour à l\'heure réelle');
    }, 10000);
  };

  const resetTime = () => {
    setSimulatedTime(null);
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed top-4 right-4 bg-purple-600/80 hover:bg-purple-700/80 text-white p-3 rounded-full backdrop-blur-sm transition-all duration-300 z-50 shadow-lg"
        title="Simulateur de temps"
      >
        🕐
      </button>
    );
  }

  return (
    <div className="fixed top-4 right-4 bg-black/90 text-white p-4 rounded-lg backdrop-blur-sm z-50 max-w-sm shadow-xl">
      <div className="flex justify-between items-start mb-3">
        <h3 className="text-lg font-bold text-purple-400">🕐 Simulateur</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white text-xl leading-none"
        >
          ×
        </button>
      </div>
      
      {simulatedTime && (
        <div className="mb-3 p-2 bg-purple-900/50 rounded text-center">
          <div className="text-yellow-400 font-bold">SIMULATION ACTIVE</div>
          <div className="text-sm">{simulatedTime.toLocaleTimeString()}</div>
          <button
            onClick={resetTime}
            className="mt-1 text-xs bg-red-600 hover:bg-red-700 px-2 py-1 rounded"
          >
            Arrêter
          </button>
        </div>
      )}
      
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {testTimes.map((time, index) => (
          <button
            key={index}
            onClick={() => simulateTime(time.hour, time.minute)}
            className="w-full text-left p-2 bg-gray-800 hover:bg-gray-700 rounded text-sm transition-colors"
            disabled={!!simulatedTime}
          >
            {time.label}
          </button>
        ))}
      </div>
      
      <div className="mt-3 text-xs text-gray-400">
        Cliquez sur un moment pour le simuler pendant 10 secondes
      </div>
    </div>
  );
};

export default TimeSimulator;
