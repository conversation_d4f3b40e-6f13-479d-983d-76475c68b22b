import React, { useEffect, useState } from 'react';
import * as SunCalc from 'suncalc';
import { useTime } from './TimeContext';

interface DebugPanelProps {
  // Pas de props pour le moment
}

const DebugPanel: React.FC<DebugPanelProps> = () => {
  const { getCurrentTime } = useTime();
  const [debugInfo, setDebugInfo] = useState<{
    currentTime: string;
    currentHour: number;
    sunrise: string;
    sunset: string;
    nauticalDawn: string;
    nauticalDusk: string;
    starsOpacity: number;
    phase: string;
    currentColors: {
      primary: string;
      secondary: string;
      tertiary: string;
    };
  } | null>(null);

  // Position par défaut (Paris)
  const userLocation = { lat: 48.8566, lon: 2.3522 };

  // Palettes de couleurs (copie de DynamicBackground.tsx)
  const TIME_COLORS = [
    // Nuit profonde (0h-4h)
    { hour: 0, colors: { primary: '#4A5568', secondary: '#34495E', tertiary: '#232B3E' } },
    { hour: 1, colors: { primary: '#4A5568', secondary: '#34495E', tertiary: '#232B3E' } },
    { hour: 2, colors: { primary: '#4A5568', secondary: '#34495E', tertiary: '#232B3E' } },
    { hour: 3, colors: { primary: '#4A5568', secondary: '#34495E', tertiary: '#232B3E' } },
    { hour: 4, colors: { primary: '#4A5568', secondary: '#34495E', tertiary: '#232B3E' } },
    // Premières lueurs de l'aube (5h-7h)
    { hour: 5, colors: { primary: '#FFD4B5', secondary: '#B4A7D6', tertiary: '#899EAF' } },
    { hour: 6, colors: { primary: '#FFD4B5', secondary: '#B4A7D6', tertiary: '#899EAF' } },
    { hour: 7, colors: { primary: '#FFD4B5', secondary: '#B4A7D6', tertiary: '#899EAF' } },
    // Journée Céleste (8h-14h)
    { hour: 8, colors: { primary: '#E6F3FF', secondary: '#C1E1F4', tertiary: '#A7C7E7' } },
    { hour: 9, colors: { primary: '#E6F3FF', secondary: '#C1E1F4', tertiary: '#A7C7E7' } },
    { hour: 10, colors: { primary: '#E6F3FF', secondary: '#C1E1F4', tertiary: '#A7C7E7' } },
    { hour: 11, colors: { primary: '#E6F3FF', secondary: '#C1E1F4', tertiary: '#A7C7E7' } },
    { hour: 12, colors: { primary: '#E6F3FF', secondary: '#C1E1F4', tertiary: '#A7C7E7' } },
    { hour: 13, colors: { primary: '#E6F3FF', secondary: '#C1E1F4', tertiary: '#A7C7E7' } },
    { hour: 14, colors: { primary: '#E6F3FF', secondary: '#C1E1F4', tertiary: '#A7C7E7' } },
    // Transition vers crépuscule (15h-17h)
    { hour: 15, colors: { primary: '#E6F3FF', secondary: '#C1E1F4', tertiary: '#A7C7E7' } },
    { hour: 16, colors: { primary: '#F0E6FA', secondary: '#D8BFD8', tertiary: '#C1E1F4' } },
    { hour: 17, colors: { primary: '#FAA0A0', secondary: '#D8BFD8', tertiary: '#E5E5FA' } },
    // Crépuscule chaleureux (18h-20h)
    { hour: 18, colors: { primary: '#FAA0A0', secondary: '#D8BFD8', tertiary: '#E5E5FA' } },
    { hour: 19, colors: { primary: '#FAA0A0', secondary: '#D8BFD8', tertiary: '#E5E5FA' } },
    { hour: 20, colors: { primary: '#FAA0A0', secondary: '#D8BFD8', tertiary: '#E5E5FA' } },
    // Transition vers nuit (21h-23h)
    { hour: 21, colors: { primary: '#6A5ACD', secondary: '#483D8B', tertiary: '#2F2F4F' } },
    { hour: 22, colors: { primary: '#4A5568', secondary: '#34495E', tertiary: '#232B3E' } },
    { hour: 23, colors: { primary: '#4A5568', secondary: '#34495E', tertiary: '#232B3E' } },
  ];

  // Fonction d'interpolation des couleurs (copie de DynamicBackground.tsx)
  const interpolateColor = (color1: string, color2: string, factor: number): string => {
    const easedFactor = factor < 0.5
      ? 2 * factor * factor
      : 1 - Math.pow(-2 * factor + 2, 2) / 2;

    const hex1 = color1.replace('#', '');
    const hex2 = color2.replace('#', '');

    const r1 = parseInt(hex1.substring(0, 2), 16);
    const g1 = parseInt(hex1.substring(2, 4), 16);
    const b1 = parseInt(hex1.substring(4, 6), 16);

    const r2 = parseInt(hex2.substring(0, 2), 16);
    const g2 = parseInt(hex2.substring(2, 4), 16);
    const b2 = parseInt(hex2.substring(4, 6), 16);

    const r = Math.round(r1 + (r2 - r1) * easedFactor);
    const g = Math.round(g1 + (g2 - g1) * easedFactor);
    const b = Math.round(b1 + (b2 - b1) * easedFactor);

    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  };

  // Calculer les couleurs actuelles
  const getCurrentColors = (currentTime: Date) => {
    const currentHour = currentTime.getHours();
    const currentMinute = currentTime.getMinutes();
    const currentSecond = currentTime.getSeconds();

    const hourProgress = (currentMinute * 60 + currentSecond) / 3600;

    const currentTimeColor = TIME_COLORS.find(tc => tc.hour === currentHour) || TIME_COLORS[0];
    const nextHour = (currentHour + 1) % 24;
    const nextTimeColor = TIME_COLORS.find(tc => tc.hour === nextHour) || TIME_COLORS[0];

    const primary = interpolateColor(currentTimeColor.colors.primary, nextTimeColor.colors.primary, hourProgress);
    const secondary = interpolateColor(currentTimeColor.colors.secondary, nextTimeColor.colors.secondary, hourProgress);
    const tertiary = interpolateColor(currentTimeColor.colors.tertiary, nextTimeColor.colors.tertiary, hourProgress);

    return { primary, secondary, tertiary };
  };

  // Calculer l'opacité des étoiles (copie de la logique d'AstronomicalLayer)
  const calculateStarsOpacity = (currentTime: Date): number => {
    const sunTimes = SunCalc.getTimes(currentTime, userLocation.lat, userLocation.lon);
    
    const sunrise = sunTimes.sunrise.getHours() + sunTimes.sunrise.getMinutes() / 60;
    const sunset = sunTimes.sunset.getHours() + sunTimes.sunset.getMinutes() / 60;
    const nauticalDusk = sunTimes.nauticalDusk.getHours() + sunTimes.nauticalDusk.getMinutes() / 60;
    const nauticalDawn = sunTimes.nauticalDawn.getHours() + sunTimes.nauticalDawn.getMinutes() / 60;
    
    const currentHour = currentTime.getHours() + currentTime.getMinutes() / 60 + currentTime.getSeconds() / 3600;

    // Période de jour complet
    if (currentHour >= sunrise && currentHour <= sunset) {
      return 0;
    }

    // CRÉPUSCULE DU SOIR
    if (currentHour > sunset && currentHour <= nauticalDusk) {
      const progress = (currentHour - sunset) / (nauticalDusk - sunset);
      return progress * 0.6;
    }

    // DÉBUT DE NUIT
    if (currentHour > nauticalDusk && currentHour <= nauticalDusk + 0.75) {
      const progress = (currentHour - nauticalDusk) / 0.75;
      return 0.6 + (progress * 0.4);
    }

    // NUIT COMPLÈTE
    if (currentHour > nauticalDusk + 0.75 && currentHour < nauticalDawn - 0.75) {
      return 1.0;
    }

    // FIN DE NUIT
    if (currentHour >= nauticalDawn - 0.75 && currentHour < nauticalDawn) {
      const progress = (currentHour - (nauticalDawn - 0.75)) / 0.75;
      return 1.0 - (progress * 0.4);
    }

    // CRÉPUSCULE DU MATIN
    if (currentHour >= nauticalDawn && currentHour < sunrise) {
      const progress = (currentHour - nauticalDawn) / (sunrise - nauticalDawn);
      return 0.6 * (1 - progress);
    }

    return 0;
  };

  // Déterminer la phase actuelle
  const getCurrentPhase = (currentTime: Date): string => {
    const sunTimes = SunCalc.getTimes(currentTime, userLocation.lat, userLocation.lon);
    
    const sunrise = sunTimes.sunrise.getHours() + sunTimes.sunrise.getMinutes() / 60;
    const sunset = sunTimes.sunset.getHours() + sunTimes.sunset.getMinutes() / 60;
    const nauticalDusk = sunTimes.nauticalDusk.getHours() + sunTimes.nauticalDusk.getMinutes() / 60;
    const nauticalDawn = sunTimes.nauticalDawn.getHours() + sunTimes.nauticalDawn.getMinutes() / 60;
    
    const currentHour = currentTime.getHours() + currentTime.getMinutes() / 60 + currentTime.getSeconds() / 3600;

    if (currentHour >= sunrise && currentHour <= sunset) return "☀️ JOUR";
    if (currentHour > sunset && currentHour <= nauticalDusk) return "🌅 CRÉPUSCULE SOIR";
    if (currentHour > nauticalDusk && currentHour <= nauticalDusk + 0.75) return "🌌 DÉBUT NUIT";
    if (currentHour > nauticalDusk + 0.75 && currentHour < nauticalDawn - 0.75) return "🌙 NUIT COMPLÈTE";
    if (currentHour >= nauticalDawn - 0.75 && currentHour < nauticalDawn) return "🌄 FIN NUIT";
    if (currentHour >= nauticalDawn && currentHour < sunrise) return "🌇 CRÉPUSCULE MATIN";
    
    return "❓ INDÉTERMINÉ";
  };

  // Mettre à jour les informations de debug
  const updateDebugInfo = () => {
    const now = getCurrentTime(); // Utiliser le temps du contexte (réel ou simulé)
    const sunTimes = SunCalc.getTimes(now, userLocation.lat, userLocation.lon);
    
    const currentHour = now.getHours() + now.getMinutes() / 60 + now.getSeconds() / 3600;
    const starsOpacity = calculateStarsOpacity(now);
    const phase = getCurrentPhase(now);
    const currentColors = getCurrentColors(now);

    setDebugInfo({
      currentTime: now.toLocaleTimeString(),
      currentHour: currentHour,
      sunrise: sunTimes.sunrise.toLocaleTimeString(),
      sunset: sunTimes.sunset.toLocaleTimeString(),
      nauticalDawn: sunTimes.nauticalDawn.toLocaleTimeString(),
      nauticalDusk: sunTimes.nauticalDusk.toLocaleTimeString(),
      starsOpacity: starsOpacity,
      phase: phase,
      currentColors: currentColors
    });
  };

  useEffect(() => {
    // Mise à jour initiale
    updateDebugInfo();
    
    // Mise à jour toutes les secondes
    const interval = setInterval(updateDebugInfo, 1000);
    
    return () => clearInterval(interval);
  }, []);

  if (!debugInfo) return null;

  return (
    <div className="fixed top-4 left-4 bg-black/80 text-white p-4 rounded-lg text-sm font-mono z-50 backdrop-blur-sm">
      <h3 className="text-yellow-400 font-bold mb-2">🔍 DEBUG ASTRONOMIQUE</h3>
      <div className="space-y-1">
        <div>⏰ Heure: <span className="text-cyan-400">{debugInfo.currentTime}</span> ({debugInfo.currentHour.toFixed(2)}h)</div>
        <div>🌅 Lever: <span className="text-orange-400">{debugInfo.sunrise}</span></div>
        <div>🌇 Coucher: <span className="text-orange-400">{debugInfo.sunset}</span></div>
        <div>🌄 Aube nautique: <span className="text-blue-400">{debugInfo.nauticalDawn}</span></div>
        <div>🌌 Crépuscule nautique: <span className="text-purple-400">{debugInfo.nauticalDusk}</span></div>
        <div>⭐ Étoiles: <span className="text-yellow-300">{(debugInfo.starsOpacity * 100).toFixed(0)}%</span></div>
        <div>📍 Phase: <span className="text-green-400">{debugInfo.phase}</span></div>
      </div>

      {/* Palette de couleurs actuelle */}
      <div className="mt-3 pt-3 border-t border-gray-600">
        <h4 className="text-sm font-bold text-cyan-400 mb-2">🎨 Palette Actuelle</h4>
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded border border-white/20"
              style={{ backgroundColor: debugInfo.currentColors.tertiary }}
            ></div>
            <span className="text-xs">Ciel: {debugInfo.currentColors.tertiary}</span>
          </div>
          <div className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded border border-white/20"
              style={{ backgroundColor: debugInfo.currentColors.secondary }}
            ></div>
            <span className="text-xs">Milieu: {debugInfo.currentColors.secondary}</span>
          </div>
          <div className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded border border-white/20"
              style={{ backgroundColor: debugInfo.currentColors.primary }}
            ></div>
            <span className="text-xs">Horizon: {debugInfo.currentColors.primary}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugPanel;
