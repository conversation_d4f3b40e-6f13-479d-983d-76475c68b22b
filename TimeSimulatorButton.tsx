import React, { useState } from 'react';
import { useTime } from './TimeContext';

interface TimeSimulatorButtonProps {
  // Pas de props pour le moment
}

const TimeSimulatorButton: React.FC<TimeSimulatorButtonProps> = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { simulatedTime, setSimulatedTime } = useTime();

  // Moments clés à tester
  const testTimes = [
    { label: '🌙 Minuit (00:00)', hour: 0, minute: 0 },
    { label: '🌄 Aube (05:30)', hour: 5, minute: 30 },
    { label: '🌅 Lever du soleil (06:30)', hour: 6, minute: 30 },
    { label: '☀️ Matin (09:00)', hour: 9, minute: 0 },
    { label: '🌞 Midi (12:00)', hour: 12, minute: 0 },
    { label: '🌤️ Après-midi (15:00)', hour: 15, minute: 0 },
    { label: '🌇 Coucher du soleil (18:30)', hour: 18, minute: 30 },
    { label: '🌆 Crépuscule (19:30)', hour: 19, minute: 30 },
    { label: '🌌 Nuit (22:00)', hour: 22, minute: 0 },
  ];

  const simulateTime = (hour: number, minute: number) => {
    const now = new Date();
    const simulated = new Date(now.getFullYear(), now.getMonth(), now.getDate(), hour, minute, 0);
    setSimulatedTime(simulated);

    // Arrêter la simulation après 15 secondes
    setTimeout(() => {
      setSimulatedTime(null);
    }, 15000);
  };

  const resetTime = () => {
    setSimulatedTime(null);
  };

  // Bouton dans le header - harmonieux avec le style existant
  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className={`px-2 sm:px-3 py-1 sm:py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors ${
          simulatedTime 
            ? 'bg-purple-600 hover:bg-purple-700 text-white animate-pulse' 
            : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
        }`}
        title="Simulateur de temps"
      >
        <span className="hidden sm:inline">🕐 </span>Simulateur
      </button>
    );
  }

  // Modal du simulateur - responsive et sans ascenseur
  return (
    <>
      {/* Overlay pour fermer en cliquant à l'extérieur */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
        onClick={() => setIsVisible(false)}
      />
      
      {/* Modal du simulateur */}
      <div className="fixed top-16 right-4 bg-gray-800/95 backdrop-blur-md text-white rounded-lg shadow-2xl border border-gray-600 z-50 w-80 max-w-[calc(100vw-2rem)]">
        {/* Header du modal */}
        <div className="flex justify-between items-center p-4 border-b border-gray-600">
          <h3 className="text-lg font-bold text-purple-400">🕐 Simulateur</h3>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-white text-xl leading-none transition-colors"
          >
            ×
          </button>
        </div>
        
        {/* Indicateur de simulation active */}
        {simulatedTime && (
          <div className="p-3 bg-purple-900/50 border-b border-gray-600">
            <div className="text-center">
              <div className="text-yellow-400 font-bold text-sm">SIMULATION ACTIVE</div>
              <div className="text-sm text-gray-300">{simulatedTime.toLocaleTimeString()}</div>
              <button
                onClick={resetTime}
                className="mt-2 text-xs bg-red-600 hover:bg-red-700 px-3 py-1 rounded transition-colors"
              >
                Arrêter
              </button>
            </div>
          </div>
        )}
        
        {/* Liste des moments - SANS ASCENSEUR */}
        <div className="p-4">
          <div className="grid grid-cols-1 gap-2">
            {testTimes.map((time, index) => (
              <button
                key={index}
                onClick={() => simulateTime(time.hour, time.minute)}
                className="w-full text-left p-3 bg-gray-700 hover:bg-gray-600 rounded-lg text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={!!simulatedTime}
              >
                {time.label}
              </button>
            ))}
          </div>
          
          {/* Instructions */}
          <div className="mt-4 pt-3 border-t border-gray-600 text-xs text-gray-400 text-center">
            Cliquez sur un moment pour le simuler pendant 15 secondes
          </div>
        </div>
      </div>
    </>
  );
};

export default TimeSimulatorButton;
